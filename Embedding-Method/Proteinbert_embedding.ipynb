{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"id": "fyQIZ18LRr8A"}, "outputs": [], "source": ["from transformers import BertModel, BertTokenizer"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ZMdSlJkCRr-H"}, "outputs": [], "source": ["# Import the required libraries\n", "''' Use the AutoTokenizer.from_pretrained and AutoModel.from_pretrained methods to load the ProteinBERT model and the corresponding tokenizer from Hugging Face's model repository.\n", " These two functions can automatically select and download the pre-trained model based on the given model name.'''\n", "from Bio import SeqIO\n", "from transformers import BertModel, BertTokenizer\n", "import torch\n", "import numpy as np\n", "import re\n", "# Loading the ProtBERT model\n", "tokenizer = BertTokenizer.from_pretrained(\"Rostlab/prot_bert\", do_lower_case=False )\n", "model = BertModel.from_pretrained(\"Rostlab/prot_bert\")\n", "def encode_sequence(sequence):\n", "    \"\"\"Encode a single protein sequence, the tokenizer converts the sequence into a format acceptable to the model, calls the ProteinBERT model to encode the sequence, and finally returns the embedding vector.\"\"\"\n", "    encoded = tokenizer(sequence, return_tensors='pt')\n", "    output = model(**encoded_input)\n", "    with torch.no_grad():\n", "        embedding = model(**encoded)['last_hidden_state']\n", "    return embedding\n", "\n", "def process_fasta(file_path, output_dir):\n", "    embeddings = {}\n", "    with open(file_path, 'r') as fasta_file:\n", "        for record in SeqIO.parse(fasta_file, 'fasta'):\n", "            sequence = str(record.seq)\n", "            sequence = \" \".join(sequence)\n", "            embedding = encode_sequence(sequence)\n", "            embeddings[record.id] = embedding.cpu().numpy()  # Convert the embedding to a NumPy array and save it\n", "\n", "            # Print information to confirm the process\n", "            print(f\"Processed Sequence ID: {record.id}, Embedding Shape: {embedding.shape}\")\n", "\n", "    # Save the embedding vector\n", "    for seq_id, emb in embeddings.items():\n", "        np.save(f\"{output_dir}/{seq_id}.npy\", emb)\n", "\n", "# Specifying the Output Directory\n", "output_dir = '/content/drive/MyDrive/new_embeddings'\n", "!mkdir -p {output_dir}\n", "\n", "# Call the function to process the FASTA file and save the embedding vector\n", "fasta_file_path = '/content/drive/MyDrive/newpractice2.fasta'\n", "process_fasta(fasta_file_path, output_dir)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 1061, "status": "ok", "timestamp": 1712124326964, "user": {"displayName": "<PERSON>", "userId": "10719115363127053574"}, "user_tz": -660}, "id": "2LKcLoBqyIO_", "outputId": "e20a9e00-4adb-42a8-c9d4-218496fbf9f1"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data shape: (1, 23, 1024)\n", "Mean: 0.0030417400412261486\n", "Standard deviation: 0.17069560289382935\n", "First few entries:\n", "[[[ 0.04684108  0.08342288  0.05901008 ... -0.07557765 -0.0041162\n", "   -0.05276055]\n", "  [ 0.10401953 -0.01991915  0.08518034 ... -0.00576243 -0.06395058\n", "   -0.01161812]\n", "  [-0.02504103 -0.01340439 -0.07927586 ... -0.01068252  0.05821817\n", "    0.02694931]\n", "  ...\n", "  [ 0.03788668 -0.00519091 -0.12194559 ... -0.09377606  0.05076564\n", "    0.05406712]\n", "  [-0.07434283 -0.08396953 -0.06405934 ... -0.07258214  0.14850846\n", "    0.10699935]\n", "  [-0.06046145 -0.00445427 -0.0329536  ... -0.02252337 -0.02030955\n", "    0.0483184 ]]]\n"]}], "source": ["import numpy as np\n", "\n", "file_path = '/content/drive/MyDrive/new_embeddings/AP02484|1|training.npy'\n", "\n", "data = np.load(file_path)\n", "\n", "print(f\"Data shape: {data.shape}\")\n", "print(f\"Mean: {np.mean(data)}\")\n", "print(f\"Standard deviation: {np.std(data)}\")\n", "print(f\"First few entries:\\n{data[:5]}\")\n"]}, {"cell_type": "markdown", "metadata": {"id": "I-VLcEO4RbzP"}, "source": []}], "metadata": {"colab": {"authorship_tag": "ABX9TyMPEhLnl9eReEBHTgj5mqqB", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}