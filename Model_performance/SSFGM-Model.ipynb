{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import random\n", "import numpy as np\n", "\n", "random.seed(42)\n", "np.random.seed(42)\n", "torch.manual_seed(42)\n", "if torch.cuda.is_available():\n", "    torch.cuda.manual_seed(42)\n", "    torch.cuda.manual_seed_all(42)  \n", "torch.backends.cudnn.deterministic = True\n", "torch.backends.cudnn.benchmark = False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import os\n", "from Bio import SeqIO\n", "def load_embeddings(npy_folder_path, max_length, embedding_type='proteinbert'):\n", "    features_dict = {}\n", "    for filename in os.listdir(npy_folder_path):\n", "        if filename.endswith('.npy'):\n", "            protein_id = filename[:-4]\n", "            feature = np.load(os.path.join(npy_folder_path, filename))\n", "            squeezed_feature = np.squeeze(feature)\n", "            if squeezed_feature.shape[0] > max_length:\n", "                padded_feature = squeezed_feature[:max_length, :]\n", "            else:\n", "                padding = np.zeros((max_length - squeezed_feature.shape[0], squeezed_feature.shape[1]))\n", "                padded_feature = np.vstack([squeezed_feature, padding])\n", "            features_dict[protein_id] = padded_feature\n", "    return features_dict\n", "\n", "def create_one_hot_features(fasta_file, max_length, amino_acids='ACDEFGHIKLMNPQRSTVWY'):\n", "    aa_to_onehot = {aa: np.eye(len(amino_acids))[i] for i, aa in enumerate(amino_acids)}\n", "    one_hot_features_dict = {}\n", "    for record in SeqIO.parse(fasta_file, \"fasta\"):\n", "        sequence_id = record.id\n", "        encoded_seq = np.array([aa_to_onehot.get(aa, np.zeros(len(amino_acids))) for aa in str(record.seq)])\n", "        if len(encoded_seq) > max_length:\n", "            encoded_seq = encoded_seq[:max_length]  \n", "        padding_length = max_length - len(encoded_seq)\n", "        if padding_length > 0:  \n", "            padded_seq = np.pad(encoded_seq, ((0, padding_length), (0, 0)), 'constant')\n", "        else:\n", "            padded_seq = encoded_seq\n", "        one_hot_features_dict[sequence_id] = padded_seq\n", "    return one_hot_features_dict\n", "\n", "# Update maximum length is 160\n", "max_length = 160\n", "\n", "def combine_features(one_hot_features, proteinbert_features, esm_features):\n", "    combined_features_dict = {}\n", "    for seq_id in one_hot_features:\n", "        if seq_id in proteinbert_features and seq_id in esm_features:\n", "            combined_feature = np.concatenate([\n", "                proteinbert_features[seq_id], \n", "                esm_features[seq_id], \n", "                one_hot_features[seq_id]\n", "            ], axis=1)\n", "            combined_features_dict[seq_id] = combined_feature\n", "    return combined_features_dict\n", "\n", "#amp_eval_combined_features = combine_features(one_hot_features, proteinbert_features, esm_features)\n", "\n", "fasta_file = 'AMP_test.fasta'\n", "proteinbert_path = 'bert_amp_test_embeddings'\n", "esm_path = 'esm_ebd_AMP_test'\n", "one_hot_features = create_one_hot_features(fasta_file, max_length)\n", "proteinbert_features = load_embeddings(proteinbert_path, max_length, 'proteinbert')\n", "esm_features = load_embeddings(esm_path, max_length, 'esm')\n", "amp_test_combined_features = combine_features(one_hot_features, proteinbert_features, esm_features)\n", "\n", "fasta_file = 'DECOY_test.fasta'\n", "proteinbert_path = 'bert_DECOY_test_embeddings'\n", "esm_path = 'esm_ebd_DECOY_test'\n", "one_hot_features = create_one_hot_features(fasta_file, max_length)\n", "proteinbert_features = load_embeddings(proteinbert_path, max_length, 'proteinbert')\n", "esm_features = load_embeddings(esm_path, max_length, 'esm')\n", "decoy_amp_test_combined_features = combine_features(one_hot_features, proteinbert_features, esm_features)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from Bio.PDB import PDBParser\n", "import torch\n", "from torch_geometric.data import Data\n", "import numpy as np\n", "import os\n", "import logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "\n", "def get_residue_positions(pdb_file, max_residues=160):\n", "    parser = PDBParser()\n", "    structure = parser.get_structure('PDB', pdb_file)\n", "    model = structure[0]  # Typically, only the first model is used\n", "\n", "    residue_positions = []\n", "    for chain in model:\n", "        for residue in chain:\n", "            if residue.id[0] == ' ' and 'CA' in residue:  # Filter out non-standard residues and ensure CA exists\n", "                residue_positions.append(residue['CA'].coord)\n", "                if len(residue_positions) >= max_residues:  # If reached max_residues, stop adding more residues\n", "                    break\n", "        if len(residue_positions) >= max_residues:\n", "            break\n", "    return residue_positions\n", "\n", "def build_edges_with_attr(residue_positions, cutoff):\n", "    edges = []\n", "    edge_attrs = []\n", "    num_residues = len(residue_positions)  # Get the number of residues (nodes)\n", "    for i in range(num_residues):\n", "        for j in range(i + 1, num_residues):\n", "            dist = np.linalg.norm(residue_positions[i] - residue_positions[j])\n", "            if dist < cutoff:\n", "                edges.append([i, j])\n", "                edges.append([j, i])\n", "                edge_attrs.append([dist])\n", "                edge_attrs.append([dist])\n", "    # Convert to tensors and add boundary check\n", "    edge_index = torch.tensor(edges, dtype=torch.long).t().contiguous()\n", "    edge_attr = torch.tensor(edge_attrs, dtype=torch.float)\n", "    \n", "    # Boundary check\n", "    if edge_index.max().item() >= num_residues:\n", "        raise ValueError(f\"Edge index out of bounds! Max index: {edge_index.max().item()}, Num residues: {num_residues}\")\n", "    \n", "    return edge_index, edge_attr\n", "\n", "def create_graph(feature_array, pdb_file, cutoff=10.0, is_amp=True, max_residues=160):\n", "    residue_positions = get_residue_positions(pdb_file, max_residues)\n", "    edge_index, edge_attr = build_edges_with_attr(residue_positions, cutoff)\n", "    x = torch.tensor(feature_array, dtype=torch.float)\n", "    y = torch.tensor([1 if is_amp else 0], dtype=torch.long)\n", "\n", "    return Data(x=x, edge_index=edge_index, edge_attr=edge_attr, y=y)\n", "\n", "def create_graphs_for_sequences(features_dict, pdb_folder, is_amp=True, max_residues=160):\n", "    graphs = {}\n", "    for seq_id, features in features_dict.items():\n", "        pdb_file = os.path.join(pdb_folder, f\"{seq_id}.pdb\")\n", "        if os.path.exists(pdb_file):\n", "            try:\n", "                graph = create_graph(features, pdb_file, is_amp=is_amp, max_residues=max_residues)\n", "                graphs[seq_id] = graph\n", "            except Exception as e:\n", "                logging.error(f\"Error processing {seq_id} from {pdb_file}: {e}\")\n", "        else:\n", "            logging.warning(f\"No PDB file found for {seq_id}\")\n", "    return graphs"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["amp_test_graphs = create_graphs_for_sequences(amp_test_combined_features, 'amp_test_pdb', is_amp=True)\n", "decoy_test_graphs = create_graphs_for_sequences(decoy_amp_test_combined_features, 'DECOY_test_pdb', is_amp=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "from torch.utils.data import Dataset, DataLoader\n", "import numpy as np\n", "import os\n", "\n", "class PeptideDataset(Dataset):\n", "    def __init__(self, root_dir, fasta_file, max_vertices=None):\n", "        self.root_dir = root_dir\n", "        self.peptides = self.parse_fasta(fasta_file)\n", "        self.max_vertices = max_vertices if max_vertices is not None else self.determine_max_vertices()\n", "\n", "    def parse_fasta(self, fasta_file):\n", "        peptides = []\n", "        with open(fasta_file, 'r') as file:\n", "            for line in file:\n", "                if line.startswith('>'):\n", "                    peptide_id = line.strip().split()[0][1:]\n", "                    peptides.append(peptide_id)\n", "        return peptides\n", "\n", "    def determine_max_vertices(self):\n", "        max_vertices = 0\n", "        for peptide_id in self.peptides:\n", "            path = os.path.join(self.root_dir, peptide_id, 'p1_input_feat.npy')\n", "            if os.path.exists(path):\n", "                current_vertices = np.load(path).shape[0]\n", "                max_vertices = max(max_vertices, current_vertices)\n", "        return max_vertices\n", "    \n", "\n", "    def __len__(self):\n", "        return len(self.peptides)\n", "\n", "    def __getitem__(self, idx):\n", "        peptide_id = self.peptides[idx]\n", "        try:\n", "            features = {\n", "                'input_feat': np.load(os.path.join(self.root_dir, peptide_id, 'p1_input_feat.npy')),\n", "                'rho_coords': np.load(os.path.join(self.root_dir, peptide_id, 'p1_rho_wrt_center.npy')),\n", "                'theta_coords': np.load(os.path.join(self.root_dir, peptide_id, 'p1_theta_wrt_center.npy')),\n", "                'mask': np.load(os.path.join(self.root_dir, peptide_id, 'p1_mask.npy'))\n", "            }\n", "\n", "            max_vertices = 5109  \n", "            for key in features:\n", "                current_length = features[key].shape[0]\n", "                if current_length < max_vertices:\n", "                    padding_shape = (max_vertices - current_length,) + features[key].shape[1:]\n", "                    padding = np.zeros(padding_shape, dtype=features[key].dtype)\n", "                    features[key] = np.concatenate((features[key], padding), axis=0)\n", "                elif current_length > max_vertices:\n", "                    features[key] = features[key][:max_vertices]\n", "                \n", "                features[key] = np.nan_to_num(features[key])\n", "\n", "            label = 0 #antimicrobial labeled 1, and non-antimicrobial labeled 0\n", "            features_tensor = {key: torch.tensor(val, dtype=torch.float32) for key, val in features.items()}\n", "            return features_tensor, torch.tensor(label, dtype=torch.long)\n", "        except Exception as e:\n", "            print(f\"Error loading data for {peptide_id}: {e}\")\n", "            return None   "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#Antimicrobial peptide \n", "root_dir = '/Workspace10/yumengzhang/xingxingpeng/test-data/finger_AMP_test'\n", "fasta_file = '/Workspace10/yumengzhang/xingxingpeng/test-data/AMP_test.fasta'\n", "model2_test_amp_dataset = PeptideDataset(root_dir=root_dir, fasta_file=fasta_file)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#Non-antimicrobial peptdide\n", "root_dir = '/Workspace10/yumengzhang/xingxingpeng/test-data/finger_DECOY_test'\n", "fasta_file = '/Workspace10/yumengzhang/xingxingpeng/test-data/DECOY_test.fasta'\n", "model2_decoy_test_amp_dataset = PeptideDataset(root_dir=root_dir, fasta_file=fasta_file)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["GCN_test = list(amp_test_graphs.values())+list(decoy_test_graphs.values())\n", "fingerprint_test = model2_test_amp_dataset + model2_decoy_test_amp_dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from torch.utils.data import Dataset\n", "from torch_geometric.data import Batch\n", "class CombinedDataset(Dataset):\n", "    def __init__(self, graph_dataset, feature_dataset):\n", "        assert len(graph_dataset) == len(feature_dataset), \"Datasets must be of the same size\"\n", "        self.graph_dataset = graph_dataset\n", "        self.feature_dataset = feature_dataset\n", "\n", "    def __len__(self):\n", "        return len(self.graph_dataset)\n", "\n", "    def __getitem__(self, idx):\n", "        graph_data = self.graph_dataset[idx]\n", "        feature_data, label = self.feature_dataset[idx]\n", "        return graph_data, feature_data, label\n", "    \n", "\n", "def custom_collate_fn(batch):\n", "    data_gcn_list = [item[0] for item in batch]  \n", "    data_masif_list = [item[1] for item in batch]  \n", "    data_gcn_batch = Batch.from_data_list(data_gcn_list)\n", "    masif_keys = data_masif_list[0].keys()\n", "    data_masif_batch = {key: torch.stack([d[key] for d in data_masif_list]) for key in masif_keys}\n", "    \n", "    return data_gcn_batch, data_masif_batch\n", "test_combined_dataset = CombinedDataset(GCN_test, fingerprint_test)\n", "test_combined_loader = DataLoader(test_combined_dataset, batch_size=32, shuffle=True, collate_fn=custom_collate_fn)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "from torch_geometric.nn import GCNConv, GATConv\n", "from torch_geometric.nn import global_mean_pool\n", "\n", "#SSFGM-Model\n", "class ImprovedGCN(torch.nn.Module):\n", "    def __init__(self, num_features, num_classes, heads=4, dropout=0.5):\n", "        super(Improved<PERSON><PERSON>, self).__init__()\n", "        # Layers that progressively reduce feature dimensions\n", "        self.conv1 = GCNConv(num_features, 1024)\n", "        self.conv2 = GCNConv(1024, 512)\n", "        self.conv3 = GCNConv(512, 256)\n", "        self.conv4 = GCNConv(256, 128)\n", "        self.conv5 = GCNConv(128, 64)\n", "        self.conv6 = GCNConv(64, 32)\n", "        \n", "        self.attn1 = GATConv(32, 16 // heads, heads=heads, concat=True)\n", "        self.fc = nn.Linear(16, num_classes)\n", "        self.dropout = dropout\n", "\n", "    def forward(self, data, return_features = False):\n", "        x, edge_index, edge_attr, batch = data.x, data.edge_index, data.edge_attr, data.batch\n", "        x = F.relu(self.conv1(x, edge_index, edge_weight=edge_attr))\n", "        x = F.dropout(x, p=self.dropout, training=self.training)\n", "        x = F.relu(self.conv2(x, edge_index, edge_weight=edge_attr))\n", "        x = F.dropout(x, p=self.dropout, training=self.training)\n", "        x = F.relu(self.conv3(x, edge_index, edge_weight=edge_attr))\n", "        x = F.dropout(x, p=self.dropout, training=self.training)\n", "        x = F.relu(self.conv4(x, edge_index, edge_weight=edge_attr))\n", "        x = F.dropout(x, p=self.dropout, training=self.training)\n", "        x = F.relu(self.conv5(x, edge_index, edge_weight=edge_attr))\n", "        x = F.dropout(x, p=self.dropout, training=self.training)\n", "        x = F.relu(self.conv6(x, edge_index, edge_weight=edge_attr))\n", "        x = F.elu(self.attn1(x, edge_index, edge_attr=edge_attr))\n", "        x = global_mean_pool(x, batch)  \n", "        if return_features:\n", "            return x\n", "        x = self.fc(x)\n", "        return F.log_softmax(x, dim=1)\n", "\n", "# Define the MaSIF_site_PyTorch model\n", "class MaSIF_site_PyTorch(nn.Module):\n", "    def __init__(self, n_thetas, n_rhos, n_feat, n_rotations, dropout_rate=0.5):\n", "        super(MaSIF_site_<PERSON><PERSON>, self).__init__()\n", "        self.n_thetas = n_thetas\n", "        self.n_rhos = n_rhos\n", "        self.n_feat = n_feat\n", "        self.n_rotations = n_rotations\n", "\n", "        # Parameters\n", "        self.mu_rho = nn.Parameter(torch.Tensor(self.n_rotations, 1))\n", "        self.sigma_rho = nn.Parameter(torch.Tensor(self.n_rotations, 1))\n", "        self.mu_theta = nn.Parameter(torch.Tensor(self.n_rotations, 1))\n", "        self.sigma_theta = nn.Parameter(torch.Tensor(self.n_rotations, 1))\n", "\n", "        # Initialize parameters\n", "        nn.init.uniform_(self.mu_rho, 0, 1)\n", "        nn.init.constant_(self.sigma_rho, 0.5)\n", "        nn.init.uniform_(self.mu_theta, 0, 2 * np.pi)\n", "        nn.init.constant_(self.sigma_theta, 0.5)\n", "\n", "        # Layers\n", "        self.avgpool1d = nn.AvgPool1d(kernel_size=6, stride=5)  # Adjust these values based on desired output size\n", "        self.fc1 = nn.Linear(40840, 2)\n", "\n", "    def forward(self, input_feat, rho_coords, theta_coords, mask, return_features = False):\n", "        batch_size, n_vertices, num_points, n_feat = input_feat.size()\n", "        input_feat = input_feat.mean(dim=2)\n", "\n", "        output_feats = []\n", "        for k in range(self.n_rotations):\n", "            rotated_theta_coords = theta_coords + k * 2 * np.pi / self.n_rotations\n", "            rotated_theta_coords %= 2 * np.pi\n", "\n", "            rho_gauss = torch.exp(-torch.square(rho_coords - self.mu_rho[k]) / (2 * torch.square(self.sigma_rho[k]) + 1e-5))\n", "            theta_gauss = torch.exp(-torch.square(rotated_theta_coords - self.mu_theta[k]) / (2 * torch.square(self.sigma_theta[k]) + 1e-5))\n", "\n", "            gauss_activations = rho_gauss * theta_gauss * mask\n", "            gauss_activations /= torch.sum(gauss_activations, dim=1, keepdim=True) + 1e-5\n", "\n", "            gauss_activations = gauss_activations.unsqueeze(3)\n", "            gauss_activations = gauss_activations.expand(-1, -1, -1, n_feat)\n", "\n", "            gauss_desc = torch.sum(gauss_activations * input_feat.unsqueeze(2), dim=2)\n", "            output_feats.append(gauss_desc)\n", "        \n", "        output_feats = torch.cat(output_feats, dim=2)\n", "        #print(output_feats.shape)\n", "        output_feats = output_feats.permute(0, 2, 1)  # [batch_size, 40, 5109]\n", "        #print(output_feats.shape)\n", "        # Apply AvgPool1d to reduce the middle dimension from 5109 to 1000\n", "        output_feats = self.avgpool1d(output_feats)  # [batch_size, 40, 1000]\n", "        #print(output_feats.shape)\n", "        output_feats = output_feats.permute(0, 2, 1)  # [batch_size, 1000, 40]\n", "        #print(output_feats.shape)\n", "        output_feats = output_feats.reshape(batch_size, -1)  # Flatten to feed into the linear layer\n", "        \n", "        if return_features:\n", "            return output_feats\n", "\n", "device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')\n", "model_gcn = ImprovedGCN(num_features=3604, num_classes=2).to(device)\n", "model_masif = MaSIF_site_PyTorch(n_thetas=16, n_rhos=5, n_feat=5, n_rotations=8).to(device)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "from torch_geometric.nn import global_mean_pool\n", "\n", "# Fusion Model Class\n", "class FusionModel(nn.Module):\n", "    def __init__(self, model_gcn, model_masif, output_features, num_classes):\n", "        super(<PERSON><PERSON><PERSON>l, self).__init__()\n", "        self.model_gcn = model_gcn\n", "        self.model_masif = model_masif\n", "        self.reduce_masif = nn.Linear(40840, 16)\n", "        self.fusion_layer = nn.Linear(output_features, 2)\n", "        \n", "\n", "    def forward(self, data_gcn, data_masif):\n", "        \n", "        gcn_features = self.model_gcn(data_gcn, return_features=True)\n", "        input_feat = data_masif['input_feat']\n", "        rho_coords = data_masif['rho_coords']\n", "        theta_coords = data_masif['theta_coords']\n", "        mask = data_masif['mask']\n", "        masif_features = self.model_masif(input_feat, rho_coords, theta_coords, mask, return_features=True)\n", "        #print(\"GCN Features Shape:\", gcn_features.shape)\n", "        #print(\"MaSIF Features Shape:\", masif_features.shape)\n", "        masif_features = F.relu(self.reduce_masif(masif_features))\n", "        # Extract features of each model\n", "        combined_features = torch.cat((gcn_features, masif_features), dim=1)\n", "        combined_features = F.relu(combined_features)\n", "        \n", "        # Classification layer\n", "        output = self.fusion_layer(combined_features)\n", "        return F.log_softmax(output, dim=1)\n", "    \n", "gcn_output_features = 32 \n", "masif_output_features = 5 * 8  \n", "total_output_features = 32\n", "\n", "fusion_model = FusionModel(model_gcn, model_masif, 32, num_classes=2).to(device)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model loaded from 9_12_GCN_finger1.pth\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Test Loss: 0.1956, Accuracy: 93.89%, AUC: 0.9732, SENS: 0.9003, SPEC: 0.9775, MCC: 0.8804\n"]}, {"data": {"image/png": "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************************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import torch\n", "import matplotlib.pyplot as plt\n", "from sklearn.metrics import roc_curve, auc, confusion_matrix, roc_auc_score, matthews_corrcoef, accuracy_score, precision_recall_curve, average_precision_score\n", "import seaborn as sns\n", "\n", "# Define a function to load the model\n", "def load_model(model, path='9_12_GCN_finger1.pth'):\n", "    model.load_state_dict(torch.load(path))\n", "    print(f'Model loaded from {path}')\n", "    return model\n", "\n", "# Define a function to draw the ROC curve\n", "def plot_roc_curve(labels, scores, title=\"ROC Curve\"):\n", "    fpr, tpr, _ = roc_curve(labels, scores)\n", "    roc_auc = auc(fpr, tpr)\n", "    #np.save('ROC_6layerGCN_finger_fpr.npy', fpr)\n", "    #np.save('ROC_6layerGCN_finger_tpr.npy', tpr)\n", "    plt.figure()\n", "    plt.plot(fpr, tpr, color='darkorange', lw=2, label=f'ROC curve (AUC = {roc_auc:.4f})')\n", "    plt.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')  \n", "    plt.xlim([0.0, 1.0])\n", "    plt.ylim([0.0, 1.05])\n", "    plt.xlabel('False Positive Rate')\n", "    plt.ylabel('True Positive Rate')\n", "    plt.title(title)\n", "    plt.legend(loc=\"lower right\")\n", "    plt.show()\n", "def plot_pr_curve(labels, scores, title=\"Precision-Recall Curve\"):\n", "    precision, recall, _ = precision_recall_curve(labels, scores)\n", "    #np.save('PR_gcn_fingerprint_precision.npy', precision)\n", "    #np.save('PR_gcn_fingerprint_recall.npy', recall)\n", "    ap = average_precision_score(labels, scores)\n", "    plt.figure()\n", "    plt.plot(recall, precision, color='blue', lw=2, label=f'PR curve (AP = {ap:.4f})')\n", "    plt.xlabel('Recall')\n", "    plt.ylabel('Precision')\n", "    plt.title(title)\n", "    plt.legend(loc=\"lower right\")\n", "    plt.show()\n", "# Use the best model to test on the test set and print all indicators\n", "def test_model_and_print_metrics(model, test_loader, criterion):\n", "    model.eval()\n", "    test_loss = 0\n", "    total_correct = 0\n", "    total_samples = 0\n", "    all_labels = []\n", "    all_scores = []  \n", "    all_preds = []   \n", "\n", "    with torch.no_grad():\n", "        for data_gcn, data_masif in test_loader:\n", "            data_gcn.to(device)\n", "            input_feat = data_masif['input_feat'].to(device)\n", "            rho_coords = data_masif['rho_coords'].to(device)\n", "            theta_coords = data_masif['theta_coords'].to(device)\n", "            mask = data_masif['mask'].to(device)\n", "            labels = data_gcn.y.to(device)\n", "            outputs = model(data_gcn, {'input_feat': input_feat, 'rho_coords': rho_coords, 'theta_coords': theta_coords, 'mask': mask})\n", "            loss = criterion(outputs, labels)\n", "            test_loss += loss.item()\n", "            scores = torch.softmax(outputs, dim=1)[:, 1].cpu().numpy()  \n", "            preds = torch.argmax(outputs, dim=1).cpu().numpy()  \n", "            labels = labels.cpu().numpy()\n", "\n", "            all_labels.extend(labels)\n", "            all_scores.extend(scores)  \n", "            all_preds.extend(preds)   \n", "            total_correct += (preds == labels).sum()\n", "            total_samples += labels.shape[0]\n", "\n", "    # Calculate the test set loss\n", "    test_loss /= len(test_loader)\n", "\n", "    #Accuracy\n", "    accuracy = total_correct / total_samples * 100\n", "\n", "    #AUC\n", "    auc_score = roc_auc_score(all_labels, all_scores)\n", "\n", "    # Calculate the confusion matrix\n", "    cm = confusion_matrix(all_labels, all_preds)\n", "    #np.save('2finger_GCN_all_labels.npy', all_labels)\n", "    #np.save('2finger_GCN_predictions.npy', all_preds)\n", "    plt.figure(figsize=(8, 6))\n", "    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', xticklabels=['Negative', 'Positive'], yticklabels=['Negative', 'Positive'])\n", "    plt.xlabel('Predicted Label')\n", "    plt.ylabel('True Label')\n", "    plt.title('Confusion Matrix')\n", "    plt.show()\n", "    tn, fp, fn, tp = cm.ravel()\n", "    # Sensitivity 和 Specificity\n", "    sensitivity = tp / (tp + fn)\n", "    specificity = tn / (tn + fp)\n", "\n", "    # MCC\n", "    mcc = matthews_corrcoef(all_labels, all_preds)\n", "\n", "\n", "    # Print all indicators\n", "    print(f'Test Loss: {test_loss:.4f}, Accuracy: {accuracy:.2f}%, AUC: {auc_score:.4f}, '\n", "          f'SENS: {sensitivity:.4f}, SPEC: {specificity:.4f}, MCC: {mcc:.4f}')\n", "    #print(f'Current learning rate: {current_lr:.8f}')\n", "    \n", "    # Draw ROC curve\n", "    plot_roc_curve(all_labels, all_scores, title=\"ROC Curve on Test Set\")\n", "    #plot_pr_curve(all_labels, all_scores, title=\"Precision-Recall Curve on Test Set\")\n", "best_model2_path = '9_12_GCN_finger1.pth' \n", "\n", "fusion_model = FusionModel(model_gcn, model_masif, 32, num_classes=2).to(device)\n", "fusion_model.to(device)\n", "fusion_model = load_model(fusion_model, best_model2_path)\n", "\n", "criterion = torch.nn.CrossEntropyLoss()\n", "test_model_and_print_metrics(fusion_model, test_combined_loader, criterion)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}