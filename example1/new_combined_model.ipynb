{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import os\n", "from Bio import SeqIO\n", "def load_embeddings(npy_folder_path, max_length, embedding_type='proteinbert'):\n", "    features_dict = {}\n", "    for filename in os.listdir(npy_folder_path):\n", "        if filename.endswith('.npy'):\n", "            protein_id = filename[:-4]\n", "            feature = np.load(os.path.join(npy_folder_path, filename))\n", "            squeezed_feature = np.squeeze(feature)\n", "            if squeezed_feature.shape[0] > max_length:\n", "                padded_feature = squeezed_feature[:max_length, :]\n", "            else:\n", "                padding = np.zeros((max_length - squeezed_feature.shape[0], squeezed_feature.shape[1]))\n", "                padded_feature = np.vstack([squeezed_feature, padding])\n", "            features_dict[protein_id] = padded_feature\n", "    return features_dict\n", "\n", "def create_one_hot_features(fasta_file, max_length, amino_acids='ACDEFGHIKLMNPQRSTVWY'):\n", "    aa_to_onehot = {aa: np.eye(len(amino_acids))[i] for i, aa in enumerate(amino_acids)}\n", "    one_hot_features_dict = {}\n", "    for record in SeqIO.parse(fasta_file, \"fasta\"):\n", "        sequence_id = record.id\n", "        encoded_seq = np.array([aa_to_onehot.get(aa, np.zeros(len(amino_acids))) for aa in str(record.seq)])\n", "        if len(encoded_seq) > max_length:\n", "            encoded_seq = encoded_seq[:max_length]  \n", "        padding_length = max_length - len(encoded_seq)\n", "        if padding_length > 0:  \n", "            padded_seq = np.pad(encoded_seq, ((0, padding_length), (0, 0)), 'constant')\n", "        else:\n", "            padded_seq = encoded_seq\n", "        one_hot_features_dict[sequence_id] = padded_seq\n", "    return one_hot_features_dict\n", "max_length = 160\n", "\n", "def combine_features(one_hot_features, proteinbert_features, esm_features):\n", "    combined_features_dict = {}\n", "    for seq_id in one_hot_features:\n", "        if seq_id in proteinbert_features and seq_id in esm_features:\n", "            combined_feature = np.concatenate([\n", "                proteinbert_features[seq_id], \n", "                esm_features[seq_id], \n", "                one_hot_features[seq_id]\n", "            ], axis=1)\n", "            combined_features_dict[seq_id] = combined_feature\n", "    return combined_features_dict"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fasta_file = '/Workspace10/yumengzhang/xingxingpeng/example/example_fasta/DECOY_eval.fasta'\n", "proteinbert_path = '/Workspace10/yumengzhang/xingxingpeng/example/example_ProteinBERT/eval_non_AMP_proteinbert'\n", "esm_path = '/Workspace10/yumengzhang/xingxingpeng/example/example_esm/example_eval_non_amp_esm'\n", "one_hot_features = create_one_hot_features(fasta_file, max_length)\n", "proteinbert_features = load_embeddings(proteinbert_path, max_length, 'proteinbert')\n", "esm_features = load_embeddings(esm_path, max_length, 'esm')\n", "decoy_amp_eval_combined_features = combine_features(one_hot_features, proteinbert_features, esm_features)\n", "\n", "fasta_file = '/Workspace10/yumengzhang/xingxingpeng/example/example_fasta/AMP_eval.fasta'\n", "proteinbert_path = '/Workspace10/yumengzhang/xingxingpeng/example/example_ProteinBERT/eval_AMP_proteinbert'\n", "esm_path = '/Workspace10/yumengzhang/xingxingpeng/example/example_esm/example_eval_AMP_esm'\n", "one_hot_features = create_one_hot_features(fasta_file, max_length)\n", "proteinbert_features = load_embeddings(proteinbert_path, max_length, 'proteinbert')\n", "esm_features = load_embeddings(esm_path, max_length, 'esm')\n", "amp_eval_combined_features = combine_features(one_hot_features, proteinbert_features, esm_features)\n", "\n", "fasta_file = '/Workspace10/yumengzhang/xingxingpeng/example/example_fasta/AMP_test.fasta'\n", "proteinbert_path = '/Workspace10/yumengzhang/xingxingpeng/example/example_ProteinBERT/test_AMP_proteinbert'\n", "esm_path = '/Workspace10/yumengzhang/xingxingpeng/example/example_esm/example_test_AMP_esm'\n", "one_hot_features = create_one_hot_features(fasta_file, max_length)\n", "proteinbert_features = load_embeddings(proteinbert_path, max_length, 'proteinbert')\n", "esm_features = load_embeddings(esm_path, max_length, 'esm')\n", "amp_test_combined_features = combine_features(one_hot_features, proteinbert_features, esm_features)\n", "\n", "fasta_file = '/Workspace10/yumengzhang/xingxingpeng/example/example_fasta/DECOY_test.fasta'\n", "proteinbert_path = '/Workspace10/yumengzhang/xingxingpeng/example/example_ProteinBERT/test_non_amp_proteinbert'\n", "esm_path = '/Workspace10/yumengzhang/xingxingpeng/example/example_esm/example_test_non_amp_esm'\n", "one_hot_features = create_one_hot_features(fasta_file, max_length)\n", "proteinbert_features = load_embeddings(proteinbert_path, max_length, 'proteinbert')\n", "esm_features = load_embeddings(esm_path, max_length, 'esm')\n", "decoy_amp_test_combined_features = combine_features(one_hot_features, proteinbert_features, esm_features)\n", "\n", "fasta_file = '/Workspace10/yumengzhang/xingxingpeng/example/example_fasta/AMP_train.fasta'\n", "proteinbert_path = '/Workspace10/yumengzhang/xingxingpeng/example/example_ProteinBERT/train_AMP_proteinbert'\n", "esm_path = '/Workspace10/yumengzhang/xingxingpeng/example/example_esm/example_train_AMP_esm'\n", "one_hot_features = create_one_hot_features(fasta_file, max_length)\n", "proteinbert_features = load_embeddings(proteinbert_path, max_length, 'proteinbert')\n", "esm_features = load_embeddings(esm_path, max_length, 'esm')\n", "amp_train_combined_features = combine_features(one_hot_features, proteinbert_features, esm_features)\n", "\n", "fasta_file = '/Workspace10/yumengzhang/xingxingpeng/example/example_fasta/DECOY_train.fasta'\n", "proteinbert_path = '/Workspace10/yumengzhang/xingxingpeng/example/example_ProteinBERT/train_non_amp_proteinbert'\n", "esm_path = '/Workspace10/yumengzhang/xingxingpeng/example/example_esm/example_train_non_amp_esm'\n", "one_hot_features = create_one_hot_features(fasta_file, max_length)\n", "proteinbert_features = load_embeddings(proteinbert_path, max_length, 'proteinbert')\n", "esm_features = load_embeddings(esm_path, max_length, 'esm')\n", "decoy_amp_train_combined_features = combine_features(one_hot_features, proteinbert_features, esm_features)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from Bio.PDB import PDBParser\n", "import torch\n", "from torch_geometric.data import Data\n", "import numpy as np\n", "import os\n", "import logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "\n", "def get_residue_positions(pdb_file, max_residues=160):\n", "    parser = PDBParser()\n", "    structure = parser.get_structure('PDB', pdb_file)\n", "    model = structure[0]  # Typically, only the first model is used\n", "\n", "    residue_positions = []\n", "    for chain in model:\n", "        for residue in chain:\n", "            if residue.id[0] == ' ' and 'CA' in residue:  # Filter out non-standard residues and ensure CA exists\n", "                residue_positions.append(residue['CA'].coord)\n", "                if len(residue_positions) >= max_residues:  # If reached max_residues, stop adding more residues\n", "                    break\n", "        if len(residue_positions) >= max_residues:\n", "            break\n", "    return residue_positions\n", "\n", "def build_edges_with_attr(residue_positions, cutoff):\n", "    edges = []\n", "    edge_attrs = []\n", "    num_residues = len(residue_positions)  # Get the number of residues (nodes)\n", "    for i in range(num_residues):\n", "        for j in range(i + 1, num_residues):\n", "            dist = np.linalg.norm(residue_positions[i] - residue_positions[j])\n", "            if dist < cutoff:\n", "                edges.append([i, j])\n", "                edges.append([j, i])\n", "                edge_attrs.append([dist])\n", "                edge_attrs.append([dist])\n", "    # Convert to tensors and add boundary check\n", "    edge_index = torch.tensor(edges, dtype=torch.long).t().contiguous()\n", "    edge_attr = torch.tensor(edge_attrs, dtype=torch.float)\n", "    \n", "    # Boundary check\n", "    if edge_index.max().item() >= num_residues:\n", "        raise ValueError(f\"Edge index out of bounds! Max index: {edge_index.max().item()}, Num residues: {num_residues}\")\n", "    \n", "    return edge_index, edge_attr\n", "\n", "def create_graph(feature_array, pdb_file, cutoff=10.0, is_amp=True, max_residues=160):\n", "    residue_positions = get_residue_positions(pdb_file, max_residues)\n", "    edge_index, edge_attr = build_edges_with_attr(residue_positions, cutoff)\n", "    x = torch.tensor(feature_array, dtype=torch.float)\n", "    y = torch.tensor([1 if is_amp else 0], dtype=torch.long)\n", "\n", "    return Data(x=x, edge_index=edge_index, edge_attr=edge_attr, y=y)\n", "\n", "def create_graphs_for_sequences(features_dict, pdb_folder, is_amp=True, max_residues=160):\n", "    graphs = {}\n", "    for seq_id, features in features_dict.items():\n", "        pdb_file = os.path.join(pdb_folder, f\"{seq_id}.pdb\")\n", "        if os.path.exists(pdb_file):\n", "            try:\n", "                graph = create_graph(features, pdb_file, is_amp=is_amp, max_residues=max_residues)\n", "                graphs[seq_id] = graph\n", "            except Exception as e:\n", "                logging.error(f\"Error processing {seq_id} from {pdb_file}: {e}\")\n", "        else:\n", "            logging.warning(f\"No PDB file found for {seq_id}\")\n", "    return graphs"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#for evaluation pdb files\n", "amp_graphs = create_graphs_for_sequences(amp_eval_combined_features, '/Workspace10/yumengzhang/xingxingpeng/example/example_PDB/expample_amp_eval_pdb', is_amp=True)\n", "decoy_graphs = create_graphs_for_sequences(decoy_amp_eval_combined_features, '/Workspace10/yumengzhang/xingxingpeng/example/example_PDB/example_non_amp_eval_pdb', is_amp=False)\n", "\n", "#for test files\n", "amp_test_graphs = create_graphs_for_sequences(amp_test_combined_features, '/Workspace10/yumengzhang/xingxingpeng/example/example_PDB/example_amp_test_pdb', is_amp=True)\n", "decoy_test_graphs = create_graphs_for_sequences(decoy_amp_test_combined_features, '/Workspace10/yumengzhang/xingxingpeng/example/example_PDB/example_non_amp_test_pdb', is_amp=False)\n", "\n", "amp_train_graphs = create_graphs_for_sequences(amp_train_combined_features, '/Workspace10/yumengzhang/xingxingpeng/example/example_PDB/example_amp_train_pdb', is_amp=True)\n", "decoy_train_graphs = create_graphs_for_sequences(decoy_amp_train_combined_features, '/Workspace10/yumengzhang/xingxingpeng/example/example_PDB/example_non_amp_train_pdb', is_amp=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "from torch.utils.data import Dataset, DataLoader\n", "import numpy as np\n", "import os\n", "\n", "class PeptideDataset(Dataset):\n", "    def __init__(self, root_dir, fasta_file, max_vertices=None):\n", "        self.root_dir = root_dir\n", "        self.peptides = self.parse_fasta(fasta_file)\n", "        self.max_vertices = max_vertices if max_vertices is not None else self.determine_max_vertices()\n", "\n", "    def parse_fasta(self, fasta_file):\n", "        peptides = []\n", "        with open(fasta_file, 'r') as file:\n", "            for line in file:\n", "                if line.startswith('>'):\n", "                    peptide_id = line.strip().split()[0][1:]\n", "                    peptides.append(peptide_id)\n", "        return peptides\n", "\n", "    def determine_max_vertices(self):\n", "        max_vertices = 0\n", "        for peptide_id in self.peptides:\n", "            path = os.path.join(self.root_dir, peptide_id, 'p1_input_feat.npy')\n", "            if os.path.exists(path):\n", "                current_vertices = np.load(path).shape[0]\n", "                max_vertices = max(max_vertices, current_vertices)\n", "        return max_vertices\n", "\n", "    def __len__(self):\n", "        return len(self.peptides)\n", "\n", "    def __getitem__(self, idx):\n", "        peptide_id = self.peptides[idx]\n", "        try:\n", "            features = {\n", "                'input_feat': np.load(os.path.join(self.root_dir, peptide_id, 'p1_input_feat.npy')),\n", "                'rho_coords': np.load(os.path.join(self.root_dir, peptide_id, 'p1_rho_wrt_center.npy')),\n", "                'theta_coords': np.load(os.path.join(self.root_dir, peptide_id, 'p1_theta_wrt_center.npy')),\n", "                'mask': np.load(os.path.join(self.root_dir, peptide_id, 'p1_mask.npy'))\n", "            }\n", "            max_vertices = 5109  # \n", "            for key in features:\n", "                current_length = features[key].shape[0]\n", "                if current_length < max_vertices:\n", "                    padding_shape = (max_vertices - current_length,) + features[key].shape[1:]\n", "                    padding = np.zeros(padding_shape, dtype=features[key].dtype)\n", "                    features[key] = np.concatenate((features[key], padding), axis=0)\n", "                elif current_length > max_vertices:\n", "                    features[key] = features[key][:max_vertices]\n", "                \n", "                features[key] = np.nan_to_num(features[key])\n", "\n", "            label = 0 \n", "            features_tensor = {key: torch.tensor(val, dtype=torch.float32) for key, val in features.items()}\n", "            return features_tensor, torch.tensor(label, dtype=torch.long)\n", "        except Exception as e:\n", "            print(f\"Error loading data for {peptide_id}: {e}\")\n", "            return None  "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["root_dir = '/Workspace10/yumengzhang/xingxingpeng/example/example_surface_feature/amp_eval'\n", "fasta_file = '/Workspace10/yumengzhang/xingxingpeng/example/example_fasta/AMP_eval.fasta'\n", "model2_eval_amp_dataset = PeptideDataset(root_dir=root_dir, fasta_file=fasta_file)\n", "\n", "root_dir = '/Workspace10/yumengzhang/xingxingpeng/example/example_surface_feature/amp_train'\n", "fasta_file = '/Workspace10/yumengzhang/xingxingpeng/example/example_fasta/AMP_train.fasta'\n", "model2_trian_amp_dataset = PeptideDataset(root_dir=root_dir, fasta_file=fasta_file)\n", "\n", "root_dir = '/Workspace10/yumengzhang/xingxingpeng/example/example_surface_feature/amp_test'\n", "fasta_file = '/Workspace10/yumengzhang/xingxingpeng/example/example_fasta/AMP_test.fasta'\n", "model2_test_amp_dataset = PeptideDataset(root_dir=root_dir, fasta_file=fasta_file)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["root_dir = '/Workspace10/yumengzhang/xingxingpeng/example/example_surface_feature/non_amp_eval'\n", "fasta_file = '/Workspace10/yumengzhang/xingxingpeng/example/example_fasta/DECOY_eval.fasta'\n", "model2_decoy_eval_amp_dataset = PeptideDataset(root_dir=root_dir, fasta_file=fasta_file)\n", "\n", "root_dir = '/Workspace10/yumengzhang/xingxingpeng/example/example_surface_feature/non_amp_test'\n", "fasta_file = '/Workspace10/yumengzhang/xingxingpeng/example/example_fasta/DECOY_test.fasta'\n", "model2_decoy_test_amp_dataset = PeptideDataset(root_dir=root_dir, fasta_file=fasta_file)\n", "\n", "root_dir = '/Workspace10/yumengzhang/xingxingpeng/example/example_surface_feature/non_amp_train'\n", "fasta_file = '/Workspace10/yumengzhang/xingxingpeng/example/example_fasta/DECOY_train.fasta'\n", "model2_decoy_trian_amp_dataset = PeptideDataset(root_dir=root_dir, fasta_file=fasta_file)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["GCN_train = list(amp_train_graphs.values()) + list(decoy_train_graphs.values())\n", "fingerprint_trian = model2_trian_amp_dataset+model2_decoy_trian_amp_dataset\n", "GCN_eval = list(amp_graphs.values()) + list(decoy_graphs.values())\n", "fingerprint_eval = model2_eval_amp_dataset+model2_decoy_eval_amp_dataset\n", "GCN_test = list(amp_test_graphs.values())+list(decoy_test_graphs.values())\n", "fingerprint_test = model2_test_amp_dataset + model2_decoy_test_amp_dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from torch.utils.data import Dataset\n", "\n", "class CombinedDataset(Dataset):\n", "    def __init__(self, graph_dataset, feature_dataset):\n", "        assert len(graph_dataset) == len(feature_dataset), \"Datasets must be of the same size\"\n", "        self.graph_dataset = graph_dataset\n", "        self.feature_dataset = feature_dataset\n", "\n", "    def __len__(self):\n", "        return len(self.graph_dataset)\n", "\n", "    def __getitem__(self, idx):\n", "        graph_data = self.graph_dataset[idx]\n", "        feature_data, label = self.feature_dataset[idx]\n", "        return graph_data, feature_data, label\n", "    \n", "train_combined_dataset = CombinedDataset(GCN_train, fingerprint_trian)\n", "test_combined_dataset = CombinedDataset(GCN_test, fingerprint_test)\n", "eval_combined_dataset = CombinedDataset(GCN_eval, fingerprint_eval)\n", "from torch_geometric.data import Batch\n", "\n", "\n", "def custom_collate_fn(batch):\n", "    data_gcn_list = [item[0] for item in batch]  \n", "    data_masif_list = [item[1] for item in batch]  \n", "\n", "    data_gcn_batch = Batch.from_data_list(data_gcn_list)\n", "\n", "   \n", "    masif_keys = data_masif_list[0].keys()\n", "    data_masif_batch = {key: torch.stack([d[key] for d in data_masif_list]) for key in masif_keys}\n", "    \n", "    return data_gcn_batch, data_masif_batch\n", "\n", "\n", "final_train_dataset = train_combined_dataset + eval_combined_dataset\n", "train_combined_loader = DataLoader(final_train_dataset, batch_size=32, shuffle=True, collate_fn=custom_collate_fn)\n", "test_combined_loader = DataLoader(test_combined_dataset, batch_size=32, shuffle=True, collate_fn=custom_collate_fn)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from torch_geometric.data import Batch\n", "\n", "\n", "def custom_collate_fn(batch):\n", "    data_gcn_list = [item[0] for item in batch]  \n", "    data_masif_list = [item[1] for item in batch] \n", "\n", "    data_gcn_batch = Batch.from_data_list(data_gcn_list)\n", "\n", "    masif_keys = data_masif_list[0].keys()\n", "    data_masif_batch = {key: torch.stack([d[key] for d in data_masif_list]) for key in masif_keys}\n", "    \n", "    return data_gcn_batch, data_masif_batch\n", "\n", "\n", "final_train_dataset = train_combined_dataset + eval_combined_dataset\n", "train_combined_loader = DataLoader(final_train_dataset, batch_size=32, shuffle=True, collate_fn=custom_collate_fn)\n", "test_combined_loader = DataLoader(test_combined_dataset, batch_size=32, shuffle=True, collate_fn=custom_collate_fn)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "from torch_geometric.nn import GCNConv, GATConv\n", "from torch_geometric.nn import global_mean_pool\n", "\n", "\n", "class ImprovedGCN(torch.nn.Module):\n", "    def __init__(self, num_features, num_classes, heads=4, dropout=0.5):\n", "        super(Improved<PERSON><PERSON>, self).__init__()\n", "        self.conv1 = GCNConv(num_features, 1024)\n", "        self.conv2 = GCNConv(1024, 512)\n", "        self.conv3 = GCNConv(512, 256)\n", "        self.conv4 = GCNConv(256, 128)\n", "        self.conv5 = GCNConv(128, 64)\n", "        self.conv6 = GCNConv(64, 32)\n", "        \n", "        self.attn1 = GATConv(32, 16 // heads, heads=heads, concat=True)\n", "        self.fc = nn.Linear(16, num_classes)\n", "        self.dropout = dropout\n", "\n", "    def forward(self, data, return_features = False):\n", "        x, edge_index, edge_attr, batch = data.x, data.edge_index, data.edge_attr, data.batch\n", "        x = F.relu(self.conv1(x, edge_index, edge_weight=edge_attr))\n", "        x = F.dropout(x, p=self.dropout, training=self.training)\n", "        x = F.relu(self.conv2(x, edge_index, edge_weight=edge_attr))\n", "        x = F.dropout(x, p=self.dropout, training=self.training)\n", "        x = F.relu(self.conv3(x, edge_index, edge_weight=edge_attr))\n", "        x = F.dropout(x, p=self.dropout, training=self.training)\n", "        x = F.relu(self.conv4(x, edge_index, edge_weight=edge_attr))\n", "        x = F.dropout(x, p=self.dropout, training=self.training)\n", "        x = F.relu(self.conv5(x, edge_index, edge_weight=edge_attr))\n", "        x = F.dropout(x, p=self.dropout, training=self.training)\n", "        x = F.relu(self.conv6(x, edge_index, edge_weight=edge_attr))\n", "        x = F.elu(self.attn1(x, edge_index, edge_attr=edge_attr))\n", "        x = global_mean_pool(x, batch)  #\n", "        if return_features:\n", "            return x\n", "        x = self.fc(x)\n", "        return F.log_softmax(x, dim=1)\n", "\n", "class MaSIF_site_PyTorch(nn.Module):\n", "    def __init__(self, n_thetas, n_rhos, n_feat, n_rotations, dropout_rate=0.5):\n", "        super(MaSIF_site_<PERSON><PERSON>, self).__init__()\n", "        self.n_thetas = n_thetas\n", "        self.n_rhos = n_rhos\n", "        self.n_feat = n_feat\n", "        self.n_rotations = n_rotations\n", "\n", "        # Parameters\n", "        self.mu_rho = nn.Parameter(torch.Tensor(self.n_rotations, 1))\n", "        self.sigma_rho = nn.Parameter(torch.Tensor(self.n_rotations, 1))\n", "        self.mu_theta = nn.Parameter(torch.Tensor(self.n_rotations, 1))\n", "        self.sigma_theta = nn.Parameter(torch.Tensor(self.n_rotations, 1))\n", "\n", "        # Initialize parameters\n", "        nn.init.uniform_(self.mu_rho, 0, 1)\n", "        nn.init.constant_(self.sigma_rho, 0.5)\n", "        nn.init.uniform_(self.mu_theta, 0, 2 * np.pi)\n", "        nn.init.constant_(self.sigma_theta, 0.5)\n", "\n", "        # Layers\n", "        self.avgpool1d = nn.AvgPool1d(kernel_size=6, stride=5)  # Adjust these values based on desired output size\n", "        self.fc1 = nn.Linear(40840, 2)\n", "\n", "    def forward(self, input_feat, rho_coords, theta_coords, mask, return_features = False):\n", "        batch_size, n_vertices, num_points, n_feat = input_feat.size()\n", "        input_feat = input_feat.mean(dim=2)\n", "\n", "        output_feats = []\n", "        for k in range(self.n_rotations):\n", "            rotated_theta_coords = theta_coords + k * 2 * np.pi / self.n_rotations\n", "            rotated_theta_coords %= 2 * np.pi\n", "\n", "            rho_gauss = torch.exp(-torch.square(rho_coords - self.mu_rho[k]) / (2 * torch.square(self.sigma_rho[k]) + 1e-5))\n", "            theta_gauss = torch.exp(-torch.square(rotated_theta_coords - self.mu_theta[k]) / (2 * torch.square(self.sigma_theta[k]) + 1e-5))\n", "\n", "            gauss_activations = rho_gauss * theta_gauss * mask\n", "            gauss_activations /= torch.sum(gauss_activations, dim=1, keepdim=True) + 1e-5\n", "\n", "            gauss_activations = gauss_activations.unsqueeze(3)\n", "            gauss_activations = gauss_activations.expand(-1, -1, -1, n_feat)\n", "\n", "            gauss_desc = torch.sum(gauss_activations * input_feat.unsqueeze(2), dim=2)\n", "            output_feats.append(gauss_desc)\n", "        \n", "        output_feats = torch.cat(output_feats, dim=2)\n", "        #print(output_feats.shape)\n", "        output_feats = output_feats.permute(0, 2, 1)  # [batch_size, 40, 5109]\n", "        #print(output_feats.shape)\n", "        # Apply AvgPool1d to reduce the middle dimension from 5109 to 1000\n", "        output_feats = self.avgpool1d(output_feats)  # [batch_size, 40, 1000]\n", "        #print(output_feats.shape)\n", "        output_feats = output_feats.permute(0, 2, 1)  # [batch_size, 1000, 40]\n", "        #print(output_feats.shape)\n", "        output_feats = output_feats.reshape(batch_size, -1)  # Flatten to feed into the linear layer\n", "        \n", "        if return_features:\n", "            return output_feats\n", "\n", "\n", "device = torch.device('cuda:3' if torch.cuda.is_available() else 'cpu')\n", "model_gcn = ImprovedGCN(num_features=3604, num_classes=2).to(device)\n", "model_masif = MaSIF_site_PyTorch(n_thetas=16, n_rhos=5, n_feat=5, n_rotations=8).to(device)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "from torch_geometric.nn import global_mean_pool\n", "\n", "class FusionModel(nn.Module):\n", "    def __init__(self, model_gcn, model_masif, output_features, num_classes):\n", "        super(<PERSON><PERSON><PERSON>l, self).__init__()\n", "        self.model_gcn = model_gcn\n", "        self.model_masif = model_masif\n", "        self.reduce_masif = nn.Linear(40840, 16)\n", "        self.fusion_layer = nn.Linear(output_features, 2)\n", "        \n", "\n", "    def forward(self, data_gcn, data_masif):\n", "        gcn_features = self.model_gcn(data_gcn, return_features=True)\n", "        input_feat = data_masif['input_feat']\n", "        rho_coords = data_masif['rho_coords']\n", "        theta_coords = data_masif['theta_coords']\n", "        mask = data_masif['mask']\n", "        masif_features = self.model_masif(input_feat, rho_coords, theta_coords, mask, return_features=True)\n", "        #print(\"GCN Features Shape:\", gcn_features.shape)\n", "        #print(\"MaSIF Features Shape:\", masif_features.shape)\n", "        masif_features = F.relu(self.reduce_masif(masif_features))\n", "        combined_features = torch.cat((gcn_features, masif_features), dim=1)\n", "        combined_features = F.relu(combined_features)\n", "        output = self.fusion_layer(combined_features)\n", "        return F.log_softmax(output, dim=1)\n", "\n", "device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')\n", "\n", "model_gcn = ImprovedGCN(num_features=3604, num_classes=2).to(device)\n", "model_masif = MaSIF_site_PyTorch(n_thetas=16, n_rhos=5, n_feat=5, n_rotations=8).to(device)\n", "\n", "\n", "gcn_output_features = 32  \n", "masif_output_features = 5 * 8  \n", "total_output_features = 32\n", "\n", "\n", "fusion_model = FusionModel(model_gcn, model_masif, 32, num_classes=2).to(device)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.metrics import roc_auc_score, confusion_matrix, matthews_corrcoef\n", "import torch\n", "import torch.nn as nn\n", "from torch.optim import AdamW\n", "from torch.optim.lr_scheduler import CosineAnnealingLR, ReduceLROnPlateau\n", "device = torch.device(\"cuda:0\" if torch.cuda.is_available() else \"cpu\")\n", "optimizer = AdamW(fusion_model.parameters(), lr=0.0001, weight_decay=1e-2)\n", "scheduler_cosine = CosineAnnealingLR(optimizer, T_max=10)\n", "scheduler_plateau = ReduceLROnPlateau(optimizer, mode='min', factor=0.1, patience=5)\n", "best_accuracy = 0.0  \n", "best_model_path = '1114_GCN_finger1.pth' \n", "\n", "criterion = nn.CrossEntropyLoss()\n", "def evaluate_model(model, data_loader, criterion, device):\n", "    model.eval() \n", "    total_loss = 0.0\n", "    total_correct = 0\n", "    total_samples = 0\n", "    all_labels = []\n", "    #all_preds = []\n", "    all_probs = [] \n", "    with torch.no_grad():  \n", "        for data_gcn, data_masif in data_loader:\n", "            data_gcn.to(device)\n", "            input_feat = data_masif['input_feat'].to(device)\n", "            rho_coords = data_masif['rho_coords'].to(device)\n", "            theta_coords = data_masif['theta_coords'].to(device)\n", "            mask = data_masif['mask'].to(device)\n", "            labels = data_gcn.y.to(device)\n", "            outputs = model(data_gcn, {'input_feat': input_feat, 'rho_coords': rho_coords, 'theta_coords': theta_coords, 'mask': mask})\n", "            loss = criterion(outputs, labels)\n", "            total_loss += loss.item()\n", "\n", "            _, predicted = torch.max(outputs.data, 1)\n", "            total_correct += (predicted == labels).sum().item()\n", "            total_samples += labels.size(0)\n", "            \n", "            all_labels.extend(labels.cpu().numpy())\n", "            probs = torch.nn.functional.softmax(outputs, dim=1)[:, 1] \n", "            all_probs.extend(probs.cpu().numpy())\n", "            #all_preds.extend(predicted.cpu().numpy())\n", "        avg_loss = total_loss / len(data_loader)\n", "        accuracy = 100 * total_correct / total_samples\n", "        auc_score = roc_auc_score(all_labels, all_probs) \n", "        tn, fp, fn, tp = confusion_matrix(all_labels, (np.array(all_probs) > 0.5).astype(int)).ravel()\n", "        sensitivity = tp / (tp + fn)\n", "        specificity = tn / (tn + fp)\n", "        mcc = matthews_corrcoef(all_labels, (np.array(all_probs) > 0.5).astype(int))\n", "\n", "    return avg_loss, accuracy, auc_score, sensitivity, specificity, mcc\n", "epochs = 20\n", "for epoch in range(epochs):\n", "    fusion_model.train()\n", "    total_loss = 0.0\n", "    total_correct = 0\n", "    total_samples = 0\n", "\n", "    for data_gcn, data_masif in train_combined_loader:\n", "        data_gcn.to(device)\n", "        input_feat = data_masif['input_feat'].to(device)\n", "        rho_coords = data_masif['rho_coords'].to(device)\n", "        theta_coords = data_masif['theta_coords'].to(device)\n", "        mask = data_masif['mask'].to(device)\n", "        labels = data_gcn.y.to(device)\n", "        optimizer.zero_grad()\n", "\n", "        outputs = fusion_model(data_gcn, {'input_feat': input_feat, 'rho_coords': rho_coords, 'theta_coords': theta_coords, 'mask': mask})\n", "\n", "        loss = criterion(outputs, labels)\n", "        total_loss += loss.item()\n", "        loss.backward()\n", "        optimizer.step()\n", "\n", "        _, predicted = torch.max(outputs.data, 1)\n", "        total_correct += (predicted == labels).sum().item()\n", "        total_samples += labels.size(0)\n", "\n", "    avg_loss = total_loss / len(train_combined_loader)\n", "    accuracy = 100 * total_correct / total_samples\n", "    print(f'Epoch {epoch+1}/{epochs}, Train Loss: {avg_loss:.4f}, Train Accuracy: {accuracy:.2f}%')\n", "\n", "    test_loss, test_accuracy, test_auc, test_sens, test_spec, test_mcc = evaluate_model(fusion_model, test_combined_loader, criterion, device)\n", "    print(f'Epoch {epoch+1}/{epochs}: Test Loss: {test_loss:.4f}, Accuracy: {test_accuracy:.2f}%, AUC: {test_auc:.4f}, SENS: {test_sens:.4f}, SPEC: {test_spec:.4f}, MCC: {test_mcc:.4f}')\n", "    if test_accuracy > best_accuracy:\n", "        best_accuracy = test_accuracy  \n", "        torch.save(fusion_model.state_dict(), best_model_path)  \n", "        print(f'Saved new best model with accuracy: {best_accuracy:.2f}%')\n", "    scheduler_cosine.step()\n", "    scheduler_plateau.step(test_loss)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}